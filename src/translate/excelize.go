package translate

import (
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
)

func ExcelHandle(filePath, sheetName, lang string) {
	if lang == "" {
		fmt.Println("请输入要翻译的语言")
		return
	}

	f, err := excelize.OpenFile(filePath)
	if err != nil {
		fmt.Printf("err: %s\n", err)
		return
	}

	// 读取Sheet内容
	rows := f.GetRows(sheetName)

	// 假设我们要修改第二列（B列）的内容
	for rowIndex, row := range rows {
		if rowIndex < 4 {
			// 如果是标题行，可以跳过修改
			continue
		}
		if len(row) > 3 {
			asterStr := GoogleTranslate(lang, row[2])
			row[3] = asterStr // 可以根据需要修改为动态内容

			// 设置修改后的内容到Excel文件
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", rowIndex+1), row[3])
		}
	}

	// 保存新的文件
	newFileName := fmt.Sprintf("locale/language_info_%s.xlsx", lang)
	if errSave := f.SaveAs(newFileName); errSave != nil {
		fmt.Println(errSave)
	} else {
		fmt.Printf("文件已保存为 %s\n", newFileName)
	}
}
