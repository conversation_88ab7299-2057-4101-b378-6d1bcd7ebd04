#!/bin/bash

# 代码质量检查脚本

echo "=== 代码质量检查开始 ==="

# 1. 格式化代码
echo "1. 格式化代码..."
go fmt ./...

# 2. 导入整理
echo "2. 整理导入..."
if command -v goimports &> /dev/null; then
    goimports -w .
else
    echo "goimports 未安装，跳过导入整理"
fi

# 3. 代码检查
echo "3. 运行 go vet..."
go vet ./...

# 4. 静态分析
echo "4. 运行静态分析..."
if command -v golangci-lint &> /dev/null; then
    golangci-lint run
else
    echo "golangci-lint 未安装，跳过静态分析"
fi

# 5. 测试覆盖率
echo "5. 运行测试并生成覆盖率报告..."
go test -coverprofile=coverage.out ./...
if [ -f coverage.out ]; then
    go tool cover -html=coverage.out -o coverage.html
    echo "覆盖率报告已生成: coverage.html"
fi

# 6. 性能测试
echo "6. 运行性能测试..."
go test -bench=. -benchmem ./...

# 7. 检查循环复杂度
echo "7. 检查代码复杂度..."
if command -v gocyclo &> /dev/null; then
    gocyclo -over 10 .
else
    echo "gocyclo 未安装，跳过复杂度检查"
fi

# 8. 检查代码重复
echo "8. 检查代码重复..."
if command -v dupl &> /dev/null; then
    dupl -threshold 50 .
else
    echo "dupl 未安装，跳过重复检查"
fi

echo "=== 代码质量检查完成 ==="
