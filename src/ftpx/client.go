package ftpx

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"tcloud/wolong/utils/plog"
	"time"

	"github.com/jlaffaye/ftp"
)

type FtpServer struct {
	c *ftp.ServerConn
	o *Options
}

func (f *FtpServer) Login(opt ...FuncOptions) error {
	f.o = newOptions(opt...)
	var err error
	f.c, err = ftp.Dial(f.o.Host, ftp.DialWithTimeout(5*time.Second))
	if err != nil {
		panic(err)
	}

	if err := f.c.Login(f.o.UserName, f.o.Password); err != nil {
		panic(err)
	}

	return nil
}

func (f *FtpServer) Close() {
	f.c.Quit()
}

func (f *FtpServer) GetRemoteBasePath() (string, error) {
	basePath, err := f.c.CurrentDir()

	if err != nil {
		return "", err
	}

	return basePath, nil
}

func (f *FtpServer) UploadDir(baseDir, localDir, basePath string) error {
	// 获取本地文件夹内容
	infos, errRead := os.ReadDir(localDir)
	if errRead != nil {
		return fmt.Errorf("reading local directory %s: %v", localDir, errRead)
	}

	plog.Info("UploadDir, localDir: ", localDir, " remotePath: ", baseDir, "basePath: ", basePath)

	lastRmPath := ""
	for _, info := range infos {
		localPath := filepath.Join(localDir, info.Name())
		remotePath := windowsToLinuxPath(filepath.Join(baseDir, info.Name()))

		if info.IsDir() {
			// 递归创建远程文件夹
			if err := f.createRemoteDir(remotePath, basePath); err != nil {
				return err
			}

			err := f.UploadDir(remotePath, localPath, basePath)
			if err != nil {
				return err
			}
		} else {
			newPath := windowsToLinuxPath(filepath.Dir(remotePath))
			if newPath != lastRmPath {
				err := f.c.ChangeDir(basePath + "/") // 切换到基础目录
				if err != nil {
					plog.Errorf("changing to base directory %s: %v", basePath, err)
					return fmt.Errorf("changing to base directory %s: %w", basePath, err)
				}

				err = f.c.ChangeDir(newPath + "/") // 切换到目标文件夹
				if err != nil {
					plog.Errorf("changing to remote directory %s: %v", basePath, err)
					return fmt.Errorf("changing to remote directory %s: %w", newPath, err)
				}

				lastRmPath = newPath
			}
			// 上传文件
			if err := f.uploadFile(remotePath, localPath); err != nil {
				return err
			}
		}
	}
	return nil
}

func (f *FtpServer) createRemoteDir(remotePath, basePath string) error {
	// 一层一层创建
	pathElements := strings.Split(remotePath, "/")

	for i := 0; i < len(pathElements); i++ {
		path := strings.Join(pathElements[:i+1], "/")
		f.makeDir(path, basePath)
	}

	return nil
}

func (f *FtpServer) makeDir(remotePath, basePath string) error {
	// 先切换到目标文件夹
	err := f.c.ChangeDir(basePath + "/")
	if err != nil {
		return fmt.Errorf("changing to remote directory %s: %w", basePath, err)
	}

	errMk := f.c.MakeDir(remotePath)
	if errMk != nil {
		return fmt.Errorf("making remote directory %s: %w", remotePath, errMk)
	}

	plog.Infof("created remote directory %s success!", basePath+"/"+remotePath)

	return nil
}

func (f *FtpServer) uploadFile(remotePath, localPath string) error {
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("opening local file %s: %v", localPath, err)
	}
	defer file.Close()

	fileName := filepath.Base(remotePath)
	// 检查远程文件是否存在
	_, err = f.c.FileSize(fileName)
	if err == nil {
		// 文件存在，删除后再上传
		err = f.c.Delete(fileName)
		if err != nil {
			plog.Errorf("deleting remote file %s: %v", fileName, err)
			return fmt.Errorf("deleting remote file %s: %v", fileName, err)
		}
	}

	err = f.c.Stor(fileName, file)
	if err != nil {
		plog.Errorf("uploading file %s to %s: %v", localPath, fileName, err)
		return fmt.Errorf("uploading file %s to %s: %v", localPath, fileName, err)
	}

	plog.Infof("uploaded file %s to %s success!", localPath, fileName)

	return nil
}

func windowsToLinuxPath(windowsPath string) string {
	// 处理盘符
	if len(windowsPath) >= 2 && windowsPath[1] == ':' {
		windowsPath = "/" + strings.ToLower(windowsPath[:1]) + windowsPath[2:]
	}

	// 替换分隔符
	linuxPath := filepath.ToSlash(windowsPath)

	return linuxPath
}
