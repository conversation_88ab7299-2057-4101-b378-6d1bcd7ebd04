package adapter

import (
	"fmt"
	"tcloud/wolong/conf"
	"tcloud/wolong/utils/plog"
)

// StructBuilderAdapter 结构构建器适配器
type StructBuilderAdapter struct{}

// NewStructBuilderAdapter 创建结构构建器适配器
func NewStructBuilderAdapter() *StructBuilderAdapter {
	return &StructBuilderAdapter{}
}

// CreateStruct 创建数据结构
func (a *StructBuilderAdapter) CreateStruct(rows [][]string, tableName string, isConstant, isMissRepeat, snakeCase bool) (map[int]interface{}, error) {
	plog.Infof("开始创建数据结构: %s", tableName)

	data := make(map[int]interface{})
	if len(rows) < conf.SysCfg.IgnoreLine {
		return nil, fmt.Errorf("错误xlsx数据格式，表头只有%v行，不足%v行", len(rows), conf.SysCfg.IgnoreLine)
	}

	// TODO: 这里需要实现实际的结构构建逻辑
	// 为了避免循环导入，我们暂时返回一个简单的实现
	// 在实际使用中，这应该调用原有的StructBuilder逻辑

	// 简单的数据处理逻辑
	for rowIdx, row := range rows {
		if rowIdx < conf.SysCfg.IgnoreLine {
			// 前四行头信息忽略掉
			continue
		}

		// 简单的ID提取逻辑（假设第一列是ID）
		id := rowIdx - conf.SysCfg.IgnoreLine
		if len(row) > 0 && row[0] != "" {
			// 这里应该有更复杂的ID解析逻辑
		}

		// 检查ID重复
		if !isMissRepeat && !isConstant && data[id] != nil {
			return nil, fmt.Errorf("解析表:%s 第%d行 数据ID重复！！！ id = %d", tableName, rowIdx, id)
		}

		// 简单的数据结构（实际应该根据表头构建复杂结构）
		value := make(map[string]interface{})
		for colIdx, cellValue := range row {
			if colIdx < len(rows[1]) {
				fieldName := rows[1][colIdx]
				value[fieldName] = cellValue
			}
		}

		data[id] = value
	}

	plog.Infof("数据结构创建完成: %s，记录数: %d", tableName, len(data))
	return data, nil
}
