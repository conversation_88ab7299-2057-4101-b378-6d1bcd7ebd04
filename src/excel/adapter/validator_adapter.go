package adapter

import (
	"encoding/json"
	"fmt"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/model"
	"tcloud/wolong/utils/checker"
	"tcloud/wolong/utils/plog"

	"github.com/spf13/cast"
)

// ValidatorAdapter 验证器适配器
type ValidatorAdapter struct {
	parser ParserInterface // 需要访问解析器的数据
}

// ParserInterface 解析器接口，用于获取数据
type ParserInterface interface {
	GetChannelXlsx() map[int32]map[string]*domain.XlsxInfo
	GetXlsx() map[string]*domain.XlsxInfo
}

// NewValidatorAdapter 创建验证器适配器
func NewValidatorAdapter(parser ParserInterface) *ValidatorAdapter {
	return &ValidatorAdapter{
		parser: parser,
	}
}

// Validate 验证数据
func (a *ValidatorAdapter) Validate(channelID int32, rows [][]string, tableName, sheetName string, checkInfo map[int32]model.ConfigCheck) error {
	plog.Infof("开始验证数据: channelID=%d tableName=%s sheetName=%s", channelID, tableName, sheetName)

	ck, exists := checkInfo[channelID]
	if !exists {
		plog.Infof("渠道 %d 无检查配置，跳过验证", channelID)
		return nil
	}

	checkInfos, exists := ck.CheckFields[sheetName]
	if !exists {
		plog.Infof("表 %s 无检查配置，跳过验证", sheetName)
		return nil
	}

	// 转换成列
	columns := a.transposeRows(rows)

	for field, checkField := range checkInfos {
		for _, ruleID := range checkField.FieldRuleID {
			checkRule, exists := ck.CheckRules[ruleID]
			if !exists {
				return fmt.Errorf("rule ID %d not found for field %s in table %s", ruleID, field, tableName)
			}

			for _, checkID := range checkRule.CheckId {
				check, exists := ck.Checks[checkID]
				if !exists {
					return fmt.Errorf("check ID %d not found for rule ID %d in table %s", checkID, ruleID, tableName)
				}

				plog.Infof("开始验证: checkid=%d tableName=%s field=%s", checkID, tableName, field)

				// 外键处理
				fkColumns, err := a.genForeignKeyColumns(check, channelID)
				if err != nil {
					return fmt.Errorf("生成外键列失败: %w", err)
				}

				// 检查
				if err := checker.ValidateField(check, tableName, field, columns[field], fkColumns); err != nil {
					return fmt.Errorf("字段验证失败: %w", err)
				}
			}
		}
	}

	plog.Infof("数据验证完成: %s.%s", tableName, sheetName)
	return nil
}

// transposeRows 将二维切片 rows 翻转成 map[string][]string
func (a *ValidatorAdapter) transposeRows(rows [][]string) map[string][]string {
	if len(rows) == 0 {
		return map[string][]string{}
	}

	// 获取列数
	numColumns := len(rows[0])
	transposed := make(map[string][]string, numColumns)

	for rowIdx, row := range rows {
		if rowIdx < conf.SysCfg.IgnoreLine {
			// 前四行头信息忽略掉
			continue
		}
		for colIndex, value := range row {
			if colIndex < len(rows[1]) {
				fieldName := rows[1][colIndex]
				transposed[fieldName] = append(transposed[fieldName], value)
			}
		}
	}

	return transposed
}

// genForeignKeyColumns 生成外键列
func (a *ValidatorAdapter) genForeignKeyColumns(ck model.Check, channelID int32) ([]string, error) {
	if ck.CheckType != checker.CheckTypeForeignKey {
		return []string{}, nil
	}

	// 解析参数 JSON
	var paramMap map[string]interface{}
	err := json.Unmarshal([]byte(ck.ParamJson), &paramMap)
	if err != nil {
		return nil, fmt.Errorf("解析检查 ID %d 的参数 JSON 失败: %w", ck.CheckID, err)
	}

	tableName := cast.ToString(paramMap["table"])
	sheetName := cast.ToString(paramMap["sheet"])
	fieldName := cast.ToString(paramMap["field"])

	// 获取Excel信息
	var xlsxInfo *domain.XlsxInfo

	// 先尝试从渠道数据获取
	channelXlsx := a.parser.GetChannelXlsx()
	if channelXlsx[channelID] != nil {
		xlsxInfo = channelXlsx[channelID][tableName]
	}

	// 如果渠道数据没有，使用主数据
	if xlsxInfo == nil {
		xlsx := a.parser.GetXlsx()
		xlsxInfo = xlsx[tableName]
		if xlsxInfo == nil {
			return nil, fmt.Errorf("生成外键列失败 channelID %d tableName %s not found", channelID, tableName)
		}
	}

	// 获取指定sheet的数据
	sheetRows, exists := xlsxInfo.Rows[sheetName]
	if !exists {
		return nil, fmt.Errorf("sheet %s not found in xlsxInfo", sheetName)
	}

	// 转换为列数据
	fkColumns := a.transposeRows(sheetRows)
	if fkColumns == nil {
		return nil, fmt.Errorf("failed to transpose rows for sheet %s", sheetName)
	}

	return fkColumns[fieldName], nil
}
