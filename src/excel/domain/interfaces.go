package domain

import (
	"tcloud/wolong/conf"
	"tcloud/wolong/model"
)

// FileLoader 文件加载器接口
type FileLoader interface {
	LoadEnumFile(filePath string) (*XlsxInfo, error)
	LoadTableFiles(tables map[string]*conf.Table, templatePath string) (map[string]*XlsxInfo, error)
	LoadChannelFiles(tables map[string]*conf.Table, channels []conf.ChannelInfo, templatePath string) (map[int32]map[string]*XlsxInfo, error)
	LoadLocaleFile(filePath string) (*XlsxInfo, error)
}

// DataProcessor 数据处理器接口
type DataProcessor interface {
	ProcessEnumSwap(data map[string]*XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error
	ProcessChannelData(channelData map[int32]map[string]*XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error
	ParseTable(channelID int32, tableName string, info *XlsxInfo, config *ProcessConfig) (map[int]interface{}, error)
	ValidateData(channelID int32, rows [][]string, tableName, sheetName string, checkInfo map[int32]model.ConfigCheck) error
}

// OutputManager 输出管理器接口
type OutputManager interface {
	OutputJSON(data map[string]map[int]interface{}, config *OutputConfig) error
	OutputLocale(localeData map[string]map[string]map[string]string, outputDir string) error
	UploadToFTP(localDir string, ftpConfig *FTPConfig) error
}

// CodeGenerator 代码生成器接口
type CodeGenerator interface {
	GenerateGolangStub(data map[string]map[int]interface{}, config *CodeGenConfig) error
	GenerateCsharpStub(data map[string]map[int]interface{}, config *CodeGenConfig) error
}

// EnumProcessor 枚举处理器接口
type EnumProcessor interface {
	BuildEnumSwapMap(enumInfo *XlsxInfo) map[string]map[string]string
	BuildParamUnfoldMap() map[string]map[string]string
	BuildLocaleSwapMap(localeInfo *XlsxInfo) map[string]map[string]map[string]string
}

// ConfigChecker 配置检查器接口
type ConfigChecker interface {
	BuildConfigCheck() map[int32]model.ConfigCheck
}

// ProcessConfig 处理配置
type ProcessConfig struct {
	EnumItems    []conf.EnumItem
	ConstantDict map[string]bool
	MissRepSheet map[string]bool
	TableType    map[string]string
	SnakeCase    bool
	CheckInfo    map[int32]model.ConfigCheck
}

// OutputConfig 输出配置
type OutputConfig struct {
	OutputDir       string
	Channels        []conf.ChannelInfo
	ProductID       int32
	ConstantDict    map[string]bool
	TableType       map[string]string
	ConsulEnabled   bool
	PlatformEnabled bool
}

// CodeGenConfig 代码生成配置
type CodeGenConfig struct {
	OutputDir    string
	StubDir      string
	SnakeCase    bool
	ConstantDict map[string]bool
	TableType    map[string]string
}

// FTPConfig FTP配置
type FTPConfig struct {
	Address  string
	User     string
	Password string
	Path     string
}

// XlsxInfo Excel信息 (从原有代码移动过来)
type XlsxInfo struct {
	TableName string
	XlsxName  string
	Rows      map[string][][]string // 子表-> [行][列]内容
	Enums     []conf.EnumItem       // 枚举表
	NameDict  map[string]string     // name -> id索引表
}

// NewXlsxInfo 创建新的XlsxInfo实例
func NewXlsxInfo() *XlsxInfo {
	info := new(XlsxInfo)
	info.Rows = make(map[string][][]string)
	return info
}
