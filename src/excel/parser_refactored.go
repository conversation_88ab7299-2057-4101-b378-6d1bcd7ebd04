package excel

import (
	"tcloud/wolong/excel/factory"
	"tcloud/wolong/excel/service"
	"tcloud/wolong/utils/plog"
)

// NewRefactoredParser 创建重构后的解析器
func NewRefactoredParser(outputDir string, stubDir string, localeDir string, genGolang, genCsharp, snakeCase, consul, ftp, plt bool) *service.ParserService {
	plog.Info("创建重构后的解析器")

	// 创建配置
	config := &service.ParserConfig{
		OutputDir:       outputDir,
		StubDir:         stubDir,
		LocaleDir:       localeDir,
		GenGolang:       genGolang,
		GenCsharp:       genCsharp,
		SnakeCase:       snakeCase,
		ConsulEnabled:   consul,
		FTPEnabled:      ftp,
		PlatformEnabled: plt,
	}

	// 使用工厂创建解析器服务
	factory := factory.NewParserFactory()
	parserService := factory.CreateParserService(config)

	plog.Info("重构后的解析器创建完成")
	return parserService
}

// RefactoredParserWrapper 重构后解析器的包装器，保持与原有接口兼容
type RefactoredParserWrapper struct {
	parserService *service.ParserService
}

// NewRefactoredParserWrapper 创建重构后解析器包装器
func NewRefactoredParserWrapper(outputDir string, stubDir string, localeDir string, genGolang, genCsharp, snakeCase, consul, ftp, plt bool) *RefactoredParserWrapper {
	parserService := NewRefactoredParser(outputDir, stubDir, localeDir, genGolang, genCsharp, snakeCase, consul, ftp, plt)

	return &RefactoredParserWrapper{
		parserService: parserService,
	}
}

// Start 启动解析流程 - 保持与原有接口兼容
func (w *RefactoredParserWrapper) Start() {
	plog.Info("启动重构后的解析流程")

	if err := w.parserService.Start(); err != nil {
		plog.ShowErrorF("解析失败: %v", err)
		return
	}

	plog.Info("重构后的解析流程完成")
}
