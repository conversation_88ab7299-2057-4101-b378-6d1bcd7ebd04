package service

import (
	"strconv"
	"strings"
	"tcloud/wolong/cmn"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/utils/plog"
)

// EnumProcessorService 枚举处理服务实现
type EnumProcessorService struct{}

// NewEnumProcessorService 创建枚举处理服务
func NewEnumProcessorService() *EnumProcessorService {
	return &EnumProcessorService{}
}

// BuildEnumSwapMap 构建枚举替换映射
func (s *EnumProcessorService) BuildEnumSwapMap(enumInfo *domain.XlsxInfo) map[string]map[string]string {
	plog.Info("开始构建枚举替换映射")

	enumSwapDict := make(map[string]map[string]string)

	if enumInfo == nil {
		plog.Info("枚举信息为空，跳过枚举替换映射构建")
		return enumSwapDict
	}

	// 解析enum表
	for subTable, rows := range enumInfo.Rows {
		plog.Infof("处理枚举子表: %s", subTable)

		// 枚举表的前三行忽略
		swapTable := make(map[string]string)
		for rowIdx, row := range rows {
			if rowIdx < 3 {
				continue
			}
			if len(row) < 2 {
				plog.Errorf("枚举表异常，数据长度=%v 第%v行", len(row), rowIdx)
				continue
			}
			swapTable[row[0]] = row[1]
		}
		enumSwapDict[subTable] = swapTable
		plog.Infof("枚举子表 %s 处理完成，映射数量: %d", subTable, len(swapTable))
	}

	plog.Infof("枚举替换映射构建完成，总子表数: %d", len(enumSwapDict))
	return enumSwapDict
}

// BuildParamUnfoldMap 构建参数展开映射
func (s *EnumProcessorService) BuildParamUnfoldMap() map[string]map[string]string {
	plog.Info("构建参数展开映射")

	// TODO: 实现参数展开逻辑
	paramUnfoldDict := make(map[string]map[string]string)

	plog.Info("参数展开映射构建完成")
	return paramUnfoldDict
}

// BuildLocaleSwapMap 构建多语言替换映射
func (s *EnumProcessorService) BuildLocaleSwapMap(localeInfo *domain.XlsxInfo) map[string]map[string]map[string]string {
	plog.Info("开始构建多语言替换映射")

	localeSwapDict := make(map[string]map[string]map[string]string)

	if localeInfo == nil {
		plog.Info("多语言信息为空，跳过多语言替换映射构建")
		return localeSwapDict
	}

	for subTable, rows := range localeInfo.Rows {
		plog.Infof("处理多语言子表: %s", subTable)

		localeSwapTable := make(map[string]map[string]string)

		// rows[1] 是语言描述栏. cn, en
		if len(rows) < 2 || len(rows[1]) < 2 {
			plog.Errorf("错误多语言数据，数据表列<2: %s", subTable)
			continue
		}

		// 语言从第1列开始，第0列是alias
		for column := 1; column < len(rows[1]); column++ {
			language := rows[1][column]
			swapTable := make(map[string]string)

			for rowIdx, row := range rows {
				if rowIdx < 3 {
					continue
				}
				if column < len(row) {
					swapTable[row[0]] = row[column]
				}
			}

			localeSwapTable[language] = swapTable
			plog.Infof("多语言 %s 处理完成，映射数量: %d", language, len(swapTable))
		}

		localeSwapDict[subTable] = localeSwapTable
	}

	plog.Infof("多语言替换映射构建完成，总子表数: %d", len(localeSwapDict))
	return localeSwapDict
}

// SwapEnum 替换枚举值
func (s *EnumProcessorService) SwapEnum(channelID int32, rows [][]string, enumItems []conf.EnumItem, enumSwapDict map[string]map[string]string) error {
	plog.Infof("开始替换枚举值，渠道ID: %d", channelID)

	for _, enumItem := range enumItems {
		if err := s.processEnumItem(rows, enumItem, enumSwapDict); err != nil {
			return err
		}
	}

	plog.Info("枚举值替换完成")
	return nil
}

// processEnumItem 处理单个枚举项
func (s *EnumProcessorService) processEnumItem(rows [][]string, enumItem conf.EnumItem, enumSwapDict map[string]map[string]string) error {
	if enumItem.Table == "enum" {
		return s.processSystemEnum(rows, enumItem, enumSwapDict)
	}

	// TODO: 处理其他表的枚举替换
	plog.Infof("跳过非系统枚举表: %s", enumItem.Table)
	return nil
}

// processSystemEnum 处理系统枚举
func (s *EnumProcessorService) processSystemEnum(rows [][]string, enumItem conf.EnumItem, enumSwapDict map[string]map[string]string) error {
	tokens := strings.Split(enumItem.Sheet, ",")
	swapTableList := make([]map[string]string, 0, len(tokens))

	for _, sheet := range tokens {
		swapTable := enumSwapDict[sheet]
		if swapTable == nil {
			plog.Errorf("enum子表%v不存在", sheet)
			return cmn.ErrNotExist
		}
		swapTableList = append(swapTableList, swapTable)
	}

	return s.swapEnumFieldMultiTable(rows, enumItem.Field, swapTableList)
}

// swapEnumFieldMultiTable 在多个表中替换枚举字段
func (s *EnumProcessorService) swapEnumFieldMultiTable(rows [][]string, field string, swapTableList []map[string]string) error {
	for column := 0; column < len(rows[0]); column++ {
		if rows[1][column] != field {
			continue
		}

		for rowIdx := 0; rowIdx < len(rows); rowIdx++ {
			// 前ignoreLine行是结构，不替换
			if rowIdx < conf.SysCfg.IgnoreLine {
				continue
			}

			// 原本就是NULL
			if strings.ToUpper(rows[rowIdx][column]) == "NULL" {
				continue
			}

			// 原本就已经是数字了，不需要枚举
			if _, err := strconv.ParseInt(rows[rowIdx][column], 10, 64); err == nil {
				continue
			}

			// 尝试在所有替换表中查找
			found := false
			for _, swapTable := range swapTableList {
				if newValue, ok := swapTable[rows[rowIdx][column]]; ok {
					rows[rowIdx][column] = newValue
					found = true
					break
				}
			}

			if !found {
				plog.Errorf("枚举值%v不存在 %d行-%d列,请检查第四行是否配置Array标签", rows[rowIdx][column], rowIdx, column)
				return cmn.ErrFail
			}
		}
	}

	return nil
}
