package service

import (
	"fmt"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/model"
	"tcloud/wolong/utils/plog"
)

// ParserService 解析器服务 - 重构后的主要解析器
type ParserService struct {
	// 依赖的服务
	fileLoader    domain.FileLoader
	dataProcessor domain.DataProcessor
	outputManager domain.OutputManager
	codeGenerator domain.CodeGenerator
	enumProcessor domain.EnumProcessor
	configChecker domain.ConfigChecker

	// 配置
	config *ParserConfig

	// 数据存储
	data *ParserData
}

// ParserConfig 解析器配置
type ParserConfig struct {
	OutputDir       string
	StubDir         string
	LocaleDir       string
	GenGolang       bool
	GenCsharp       bool
	SnakeCase       bool
	ConsulEnabled   bool
	FTPEnabled      bool
	PlatformEnabled bool
}

// ParserData 解析器数据
type ParserData struct {
	// 原始数据
	Enum        *domain.XlsxInfo
	Locale      *domain.XlsxInfo
	Xlsx        map[string]*domain.XlsxInfo
	ChannelXlsx map[int32]map[string]*domain.XlsxInfo

	// 中间数据
	EnumSwapDict    map[string]map[string]string
	ParamUnfoldDict map[string]map[string]string
	LocaleSwapDict  map[string]map[string]map[string]string
	Output          map[string]map[int]interface{}
	ConstantDict    map[string]bool
	MissChkRepSheet map[string]bool
	TableType       map[string]string
	CheckInfo       map[int32]model.ConfigCheck
}

// NewParserService 创建解析器服务
func NewParserService(
	fileLoader domain.FileLoader,
	dataProcessor domain.DataProcessor,
	outputManager domain.OutputManager,
	codeGenerator domain.CodeGenerator,
	enumProcessor domain.EnumProcessor,
	configChecker domain.ConfigChecker,
	config *ParserConfig,
) *ParserService {
	return &ParserService{
		fileLoader:    fileLoader,
		dataProcessor: dataProcessor,
		outputManager: outputManager,
		codeGenerator: codeGenerator,
		enumProcessor: enumProcessor,
		configChecker: configChecker,
		config:        config,
		data:          newParserData(),
	}
}

// newParserData 创建解析器数据
func newParserData() *ParserData {
	return &ParserData{
		Xlsx:            make(map[string]*domain.XlsxInfo),
		ChannelXlsx:     make(map[int32]map[string]*domain.XlsxInfo),
		EnumSwapDict:    make(map[string]map[string]string),
		ParamUnfoldDict: make(map[string]map[string]string),
		LocaleSwapDict:  make(map[string]map[string]map[string]string),
		Output:          make(map[string]map[int]interface{}),
		ConstantDict:    make(map[string]bool),
		MissChkRepSheet: make(map[string]bool),
		TableType:       make(map[string]string),
		CheckInfo:       make(map[int32]model.ConfigCheck),
	}
}

// Start 启动解析流程 - 重构后的主入口
func (p *ParserService) Start() error {
	plog.Info("开始Excel解析流程")

	// 1. 加载文件
	if err := p.loadFiles(); err != nil {
		return fmt.Errorf("加载文件失败: %w", err)
	}

	// 2. 准备中间数据
	if err := p.prepareData(); err != nil {
		return fmt.Errorf("准备数据失败: %w", err)
	}

	// 3. 解析数据
	if err := p.parseData(); err != nil {
		return fmt.Errorf("解析数据失败: %w", err)
	}

	plog.Info("解析Excel文件成功")

	// 4. 输出数据
	if err := p.outputData(); err != nil {
		return fmt.Errorf("输出数据失败: %w", err)
	}

	// 5. 生成代码
	if err := p.generateCode(); err != nil {
		return fmt.Errorf("生成代码失败: %w", err)
	}

	// 6. FTP上传
	if p.config.FTPEnabled {
		if err := p.uploadToFTP(); err != nil {
			return fmt.Errorf("FTP上传失败: %w", err)
		}
	}

	plog.Info("Excel解析流程完成")
	return nil
}

// loadFiles 加载文件
func (p *ParserService) loadFiles() error {
	plog.Info("开始加载文件")

	// 加载枚举文件
	enumInfo, err := p.fileLoader.LoadEnumFile(conf.SysCfg.EnumFile)
	if err != nil {
		return fmt.Errorf("加载枚举文件失败: %w", err)
	}
	p.data.Enum = enumInfo

	// 加载表格文件
	templatePath := conf.SysCfg.Template.Path
	xlsxData, err := p.fileLoader.LoadTableFiles(conf.Cfg.Tables, templatePath)
	if err != nil {
		return fmt.Errorf("加载表格文件失败: %w", err)
	}
	p.data.Xlsx = xlsxData

	// 设置表格类型和特殊标记
	p.setTableMetadata()

	// 加载渠道文件
	channelData, err := p.fileLoader.LoadChannelFiles(conf.Cfg.Tables, conf.SysCfg.Channel, templatePath)
	if err != nil {
		return fmt.Errorf("加载渠道文件失败: %w", err)
	}
	p.data.ChannelXlsx = channelData

	// 加载多语言文件
	localeInfo, err := p.fileLoader.LoadLocaleFile(conf.SysCfg.LocaleFile)
	if err != nil {
		plog.Infof("加载多语言文件失败，跳过: %v", err)
	}
	p.data.Locale = localeInfo

	plog.Info("文件加载完成")
	return nil
}

// setTableMetadata 设置表格元数据
func (p *ParserService) setTableMetadata() {
	for tblName, tbl := range conf.Cfg.Tables {
		if tbl.Duplicate {
			continue
		}

		p.data.TableType[tblName] = tbl.TableType

		if tbl.Constant {
			p.data.ConstantDict[tblName] = true
		}

		if tbl.MissRepeat {
			p.data.MissChkRepSheet[tblName] = true
		}
	}
}

// prepareData 准备中间数据
func (p *ParserService) prepareData() error {
	plog.Info("开始准备中间数据")

	// 构建枚举替换结构
	p.data.EnumSwapDict = p.enumProcessor.BuildEnumSwapMap(p.data.Enum)

	// 构建参数展开结构
	p.data.ParamUnfoldDict = p.enumProcessor.BuildParamUnfoldMap()

	// 构建多语言替换结构
	if p.data.Locale != nil {
		p.data.LocaleSwapDict = p.enumProcessor.BuildLocaleSwapMap(p.data.Locale)
	}

	// 构建配置检查结构
	if p.config.PlatformEnabled {
		p.data.CheckInfo = p.configChecker.BuildConfigCheck()
	}

	plog.Info("中间数据准备完成")
	return nil
}

// parseData 解析数据
func (p *ParserService) parseData() error {
	plog.Info("开始解析数据")

	// 替换系统枚举
	if err := p.dataProcessor.ProcessEnumSwap(p.data.Xlsx, p.data.EnumSwapDict, conf.SysCfg.IgnoreLine); err != nil {
		return fmt.Errorf("处理枚举替换失败: %w", err)
	}

	// 处理渠道数据枚举替换
	if err := p.dataProcessor.ProcessChannelData(p.data.ChannelXlsx, p.data.EnumSwapDict, conf.SysCfg.IgnoreLine); err != nil {
		return fmt.Errorf("处理渠道数据失败: %w", err)
	}

	// 解析每个表格
	processConfig := &domain.ProcessConfig{
		ConstantDict: p.data.ConstantDict,
		MissRepSheet: p.data.MissChkRepSheet,
		TableType:    p.data.TableType,
		SnakeCase:    p.config.SnakeCase,
		CheckInfo:    p.data.CheckInfo,
	}

	for tableName, info := range p.data.Xlsx {
		totalStructs, err := p.dataProcessor.ParseTable(conf.DefaultChannel, tableName, info, processConfig)
		if err != nil {
			return fmt.Errorf("解析表格 %s 失败: %w", tableName, err)
		}
		p.data.Output[tableName] = totalStructs
	}

	plog.Info("数据解析完成")
	return nil
}

// outputData 输出数据
func (p *ParserService) outputData() error {
	plog.Info("开始输出数据")

	// 输出JSON
	outputConfig := &domain.OutputConfig{
		OutputDir:       p.config.OutputDir,
		Channels:        conf.SysCfg.Channel,
		ProductID:       int32(conf.SysCfg.ProductId),
		ConstantDict:    p.data.ConstantDict,
		TableType:       p.data.TableType,
		ConsulEnabled:   p.config.ConsulEnabled,
		PlatformEnabled: p.config.PlatformEnabled,
	}

	if err := p.outputManager.OutputJSON(p.data.Output, outputConfig); err != nil {
		return fmt.Errorf("输出JSON失败: %w", err)
	}

	// 输出多语言文件
	if p.data.LocaleSwapDict != nil {
		if err := p.outputManager.OutputLocale(p.data.LocaleSwapDict, p.config.LocaleDir); err != nil {
			return fmt.Errorf("输出多语言文件失败: %w", err)
		}
	}

	plog.Info("数据输出完成")
	return nil
}

// generateCode 生成代码
func (p *ParserService) generateCode() error {
	if !p.config.GenGolang && !p.config.GenCsharp {
		return nil
	}

	plog.Info("开始生成代码")

	codeGenConfig := &domain.CodeGenConfig{
		OutputDir:    p.config.OutputDir,
		StubDir:      p.config.StubDir,
		SnakeCase:    p.config.SnakeCase,
		ConstantDict: p.data.ConstantDict,
		TableType:    p.data.TableType,
	}

	if p.config.GenGolang {
		if err := p.codeGenerator.GenerateGolangStub(p.data.Output, codeGenConfig); err != nil {
			return fmt.Errorf("生成Golang代码失败: %w", err)
		}
	}

	if p.config.GenCsharp {
		if err := p.codeGenerator.GenerateCsharpStub(p.data.Output, codeGenConfig); err != nil {
			return fmt.Errorf("生成C#代码失败: %w", err)
		}
	}

	plog.Info("代码生成完成")
	return nil
}

// uploadToFTP 上传到FTP
func (p *ParserService) uploadToFTP() error {
	plog.Info("开始FTP上传")

	ftpConfig := &domain.FTPConfig{
		Address:  conf.SysCfg.FtpInfo.Address,
		User:     conf.SysCfg.FtpInfo.User,
		Password: conf.SysCfg.FtpInfo.Pwd,
		Path:     conf.SysCfg.FtpInfo.Path,
	}

	if err := p.outputManager.UploadToFTP(p.config.OutputDir, ftpConfig); err != nil {
		return fmt.Errorf("FTP上传失败: %w", err)
	}

	plog.Info("FTP上传完成")
	return nil
}
