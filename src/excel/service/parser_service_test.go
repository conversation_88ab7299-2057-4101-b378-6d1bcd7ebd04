package service

import (
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/model"
	"testing"
)

// MockFileLoader 模拟文件加载器
type MockFileLoader struct{}

func (m *MockFileLoader) LoadEnumFile(filePath string) (*domain.XlsxInfo, error) {
	return &domain.XlsxInfo{
		TableName: "enum",
		XlsxName:  filePath,
		Rows:      make(map[string][][]string),
		NameDict:  make(map[string]string),
	}, nil
}

func (m *MockFileLoader) LoadTableFiles(tables map[string]*conf.Table, templatePath string) (map[string]*domain.XlsxInfo, error) {
	result := make(map[string]*domain.XlsxInfo)
	for tableName := range tables {
		result[tableName] = &domain.XlsxInfo{
			TableName: tableName,
			XlsxName:  "test.xlsx",
			Rows:      make(map[string][][]string),
			NameDict:  make(map[string]string),
		}
	}
	return result, nil
}

func (m *MockFileLoader) LoadChannelFiles(tables map[string]*conf.Table, channels []conf.ChannelInfo, templatePath string) (map[int32]map[string]*domain.XlsxInfo, error) {
	return make(map[int32]map[string]*domain.XlsxInfo), nil
}

func (m *MockFileLoader) LoadLocaleFile(filePath string) (*domain.XlsxInfo, error) {
	return nil, nil
}

// MockDataProcessor 模拟数据处理器
type MockDataProcessor struct{}

func (m *MockDataProcessor) ProcessEnumSwap(data map[string]*domain.XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error {
	return nil
}

func (m *MockDataProcessor) ProcessChannelData(channelData map[int32]map[string]*domain.XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error {
	return nil
}

func (m *MockDataProcessor) ParseTable(channelID int32, tableName string, info *domain.XlsxInfo, config *domain.ProcessConfig) (map[int]interface{}, error) {
	return make(map[int]interface{}), nil
}

func (m *MockDataProcessor) ValidateData(channelID int32, rows [][]string, tableName, sheetName string, checkInfo map[int32]model.ConfigCheck) error {
	return nil
}

// MockOutputManager 模拟输出管理器
type MockOutputManager struct{}

func (m *MockOutputManager) OutputJSON(data map[string]map[int]interface{}, config *domain.OutputConfig) error {
	return nil
}

func (m *MockOutputManager) OutputLocale(localeData map[string]map[string]map[string]string, outputDir string) error {
	return nil
}

func (m *MockOutputManager) UploadToFTP(localDir string, ftpConfig *domain.FTPConfig) error {
	return nil
}

// MockCodeGenerator 模拟代码生成器
type MockCodeGenerator struct{}

func (m *MockCodeGenerator) GenerateGolangStub(data map[string]map[int]interface{}, config *domain.CodeGenConfig) error {
	return nil
}

func (m *MockCodeGenerator) GenerateCsharpStub(data map[string]map[int]interface{}, config *domain.CodeGenConfig) error {
	return nil
}

// MockEnumProcessor 模拟枚举处理器
type MockEnumProcessor struct{}

func (m *MockEnumProcessor) BuildEnumSwapMap(enumInfo *domain.XlsxInfo) map[string]map[string]string {
	return make(map[string]map[string]string)
}

func (m *MockEnumProcessor) BuildParamUnfoldMap() map[string]map[string]string {
	return make(map[string]map[string]string)
}

func (m *MockEnumProcessor) BuildLocaleSwapMap(localeInfo *domain.XlsxInfo) map[string]map[string]map[string]string {
	return make(map[string]map[string]map[string]string)
}

// MockConfigChecker 模拟配置检查器
type MockConfigChecker struct{}

func (m *MockConfigChecker) BuildConfigCheck() map[int32]model.ConfigCheck {
	return make(map[int32]model.ConfigCheck)
}

// TestParserService_Components 测试解析器服务的组件
func TestParserService_Components(t *testing.T) {
	// 测试各个组件的创建
	var fileLoader domain.FileLoader = &MockFileLoader{}
	var dataProcessor domain.DataProcessor = &MockDataProcessor{}
	var outputManager domain.OutputManager = &MockOutputManager{}
	var codeGenerator domain.CodeGenerator = &MockCodeGenerator{}
	var enumProcessor domain.EnumProcessor = &MockEnumProcessor{}
	var configChecker domain.ConfigChecker = &MockConfigChecker{}

	// 创建配置
	config := &ParserConfig{
		OutputDir:       "test_output",
		StubDir:         "test_stub",
		LocaleDir:       "test_locale",
		GenGolang:       false,
		GenCsharp:       false,
		SnakeCase:       false,
		ConsulEnabled:   false,
		FTPEnabled:      false,
		PlatformEnabled: false,
	}

	// 创建解析器服务
	parserService := NewParserService(
		fileLoader,
		dataProcessor,
		outputManager,
		codeGenerator,
		enumProcessor,
		configChecker,
		config,
	)

	// 测试组件是否正确创建
	if parserService == nil {
		t.Error("ParserService should not be nil")
	}

	if parserService.config != config {
		t.Error("ParserService config should match input config")
	}

	t.Log("ParserService components created successfully")
}
