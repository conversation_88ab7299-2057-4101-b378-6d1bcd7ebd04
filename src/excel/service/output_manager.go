package service

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"tcloud/wolong/conf"
	"tcloud/wolong/deploy"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/ftpx"
	"tcloud/wolong/model"
	"tcloud/wolong/utils"
	"tcloud/wolong/utils/git"
	"tcloud/wolong/utils/plog"
	"time"
)

// OutputManagerService 输出管理服务实现
type OutputManagerService struct{}

// NewOutputManagerService 创建输出管理服务
func NewOutputManagerService() *OutputManagerService {
	return &OutputManagerService{}
}

// OutputJSON 输出JSON文件
func (s *OutputManagerService) OutputJSON(data map[string]map[int]interface{}, config *domain.OutputConfig) error {
	plog.Info("开始输出JSON文件")

	// 确保输出目录存在
	if err := utils.PathCheckDone(config.OutputDir); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 获取渠道列表
	channelList := s.getChannelList(config.Channels)

	// 为每个渠道输出文件
	for _, channel := range config.Channels {
		if err := s.outputChannelData(channel, data, config, channelList); err != nil {
			return fmt.Errorf("输出渠道 %d 数据失败: %w", channel.Id, err)
		}
	}

	plog.Info("JSON文件输出完成")
	return nil
}

// OutputLocale 输出多语言文件
func (s *OutputManagerService) OutputLocale(localeData map[string]map[string]map[string]string, outputDir string) error {
	plog.Info("开始输出多语言文件")

	// 校验localeDir是否存在
	if err := os.MkdirAll(outputDir, 0777); err != nil {
		return fmt.Errorf("创建多语言目录失败: %w", err)
	}

	// 导出locale文件
	for tableName, localeSwapDict := range localeData {
		for locale, swapTable := range localeSwapDict {
			localeDir := fmt.Sprintf("%s/%s", outputDir, locale)
			if err := os.MkdirAll(localeDir, 0777); err != nil {
				plog.Errorf("创建多语言目录 %s 失败", localeDir)
				continue
			}

			filePath := fmt.Sprintf("%s/%s.json", localeDir, tableName)
			if err := s.writeJSONFile(filePath, swapTable); err != nil {
				plog.Errorf("生成多语言文件 %s 失败: %v", filePath, err)
				continue
			}
		}
	}

	plog.Info("多语言文件输出完成")
	return nil
}

// UploadToFTP 上传到FTP
func (s *OutputManagerService) UploadToFTP(localDir string, ftpConfig *domain.FTPConfig) error {
	plog.Infof("开始FTP上传: %s", localDir)

	ftpServer := &ftpx.FtpServer{}
	err := ftpServer.Login(
		ftpx.WithHost(ftpConfig.Address),
		ftpx.WithUserName(ftpConfig.User),
		ftpx.WithPassword(ftpConfig.Password),
		ftpx.WithBaseDir(ftpConfig.Path))
	if err != nil {
		return fmt.Errorf("FTP登录失败: %w", err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.GetRemoteBasePath()
	if err != nil {
		return fmt.Errorf("获取FTP基础路径失败: %w", err)
	}

	if err := ftpServer.UploadDir(ftpConfig.Path, localDir, basePath); err != nil {
		return fmt.Errorf("FTP上传失败: %w", err)
	}

	plog.Info("FTP上传完成")
	return nil
}

// getChannelList 获取渠道列表
func (s *OutputManagerService) getChannelList(channels []conf.ChannelInfo) []string {
	channelList := make([]string, 0, len(channels))
	for _, info := range channels {
		channelList = append(channelList, strconv.Itoa(int(info.Id)))
	}
	return channelList
}

// outputChannelData 输出渠道数据
func (s *OutputManagerService) outputChannelData(channel conf.ChannelInfo, data map[string]map[int]interface{}, config *domain.OutputConfig, channelList []string) error {
	plog.Infof("输出渠道（%s-%d）配置文件", channel.Name, channel.Id)

	// 创建渠道目录
	channelPath, err := s.createChannelDirectory(channel.Id, config.OutputDir, config.ProductID)
	if err != nil {
		return fmt.Errorf("创建渠道目录失败: %w", err)
	}

	// 初始化上传信息
	uploadInfo := s.initUploadInfo(channel.Id, channelList, config.ProductID, len(data))
	uploadList := make([]model.UploadInfo, 0, len(data))

	cnt := 0
	for tableName, outputData := range data {
		cnt++
		if !conf.OutputJson(tableName) {
			continue
		}

		if err := s.processTable(channel.Id, channelPath, tableName, outputData, &uploadInfo, cnt, config); err != nil {
			return fmt.Errorf("处理表格 %s 失败: %w", tableName, err)
		}

		if config.PlatformEnabled {
			uploadList = append(uploadList, uploadInfo)
		}
	}

	// 上传到平台
	if config.PlatformEnabled {
		s.uploadToPlatform(uploadList)
	}

	return nil
}

// createChannelDirectory 创建渠道目录
func (s *OutputManagerService) createChannelDirectory(channelID int32, outputDir string, productID int32) (string, error) {
	channelPath := fmt.Sprintf("%s/%d/%d", outputDir, productID, channelID)
	if err := utils.PathCheckDone(channelPath); err != nil {
		return "", err
	}
	return channelPath, nil
}

// initUploadInfo 初始化上传信息
func (s *OutputManagerService) initUploadInfo(channelID int32, channelList []string, productID int32, dataCount int) model.UploadInfo {
	serialNoNow := fmt.Sprintf("%d-%s", channelID, time.Now().Format("20060102150405"))
	uploadInfo := model.UploadInfo{}
	uploadInfo.SerialNo = serialNoNow
	uploadInfo.EndpointName, _ = os.Hostname()
	uploadInfo.EnvType = git.GetEnvTypeByGitBranch()
	uploadInfo.ProductId = int(productID)
	uploadInfo.ChannelId = channelID
	uploadInfo.GitName, _ = git.GetGitUserName()
	uploadInfo.GitLog, _ = git.GetLatestCommit()
	uploadInfo.ClientIp = utils.LocalIP()
	uploadInfo.RowCnt = dataCount * len(channelList)
	uploadInfo.ChannelStr = strings.Join(channelList, ",")
	return uploadInfo
}

// processTable 处理单个表格
func (s *OutputManagerService) processTable(channelID int32, channelPath, tableName string, outputData map[int]interface{}, uploadInfo *model.UploadInfo, cnt int, config *domain.OutputConfig) error {
	filePath := fmt.Sprintf("%s/%s.json", channelPath, tableName)

	// 准备输出数据
	var realOutput []byte
	var err error

	if config.ConstantDict[tableName] {
		// 常量表处理
		bsObj, err := json.Marshal(outputData[0])
		if err != nil {
			return fmt.Errorf("序列化常量表失败: %w", err)
		}
		str, _ := utils.RemoveJSONField(string(bsObj), "id")
		realOutput = []byte(str)
	} else {
		// 普通表处理
		realOutput, err = json.Marshal(outputData)
		if err != nil {
			return fmt.Errorf("序列化表数据失败: %w", err)
		}
	}

	// 写入文件
	if err := s.writeFile(filePath, realOutput); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	// 更新上传信息
	s.updateUploadInfo(uploadInfo, tableName, realOutput, cnt, config.TableType[tableName])

	// 发布到Consul
	if config.ConsulEnabled {
		s.publishToConsul(channelID, tableName, realOutput, config.TableType[tableName])
	}

	return nil
}

// writeFile 写入文件
func (s *OutputManagerService) writeFile(filePath string, data []byte) error {
	outputFile, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer outputFile.Close()

	_, err = outputFile.Write(data)
	return err
}

// writeJSONFile 写入JSON文件
func (s *OutputManagerService) writeJSONFile(filePath string, data interface{}) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	return encoder.Encode(data)
}

// updateUploadInfo 更新上传信息
func (s *OutputManagerService) updateUploadInfo(uploadInfo *model.UploadInfo, tableName string, data []byte, cnt int, tableType string) {
	xlsxName := conf.Cfg.Tables[tableName].Workbook
	sheetUp := &model.ExcelSheet{
		FileName:  xlsxName,
		SheetName: tableName,
		Md5:       git.Json2MD5Hex(data),
		CtxJson:   string(data),
	}
	uploadInfo.ExcelSheet = sheetUp
	uploadInfo.RowIndex = cnt
	uploadInfo.TableType = tableType
}

// publishToConsul 发布到Consul
func (s *OutputManagerService) publishToConsul(channelID int32, tableName string, data []byte, tableType string) {
	switch tableType {
	case "", conf.TableTypeShared, conf.TableTypeServerOnly:
		deploy.Publish2Consul(channelID, tableName, data)
		plog.Infof("导入到Consul成功: %s", conf.SysCfg.ConsulAddress)
	}
}

// uploadToPlatform 上传到平台
func (s *OutputManagerService) uploadToPlatform(uploadList []model.UploadInfo) {
	if len(uploadList) > 0 {
		envName, _ := git.GetCurrentBranch()
		plog.Infof("开始上传到platform，产品ID：%d，渠道：%d，环境：%s", uploadList[0].ProductId, uploadList[0].ChannelId, envName)
		deploy.Upload2Platform(uploadList)
	}
}
