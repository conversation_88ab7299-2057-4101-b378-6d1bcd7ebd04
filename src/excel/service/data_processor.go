package service

import (
	"fmt"
	"strings"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/model"
	"tcloud/wolong/utils"
	"tcloud/wolong/utils/plog"
)

// DataProcessorService 数据处理服务实现
type DataProcessorService struct {
	enumProcessor EnumProcessorInterface
	structBuilder StructBuilderInterface
	validator     ValidatorInterface
}

// EnumProcessorInterface 枚举处理器接口
type EnumProcessorInterface interface {
	SwapEnum(channelID int32, rows [][]string, enumItems []conf.EnumItem, enumSwapDict map[string]map[string]string) error
}

// StructBuilderInterface 结构构建器接口
type StructBuilderInterface interface {
	CreateStruct(rows [][]string, tableName string, isConstant, isMissRepeat, snakeCase bool) (map[int]interface{}, error)
}

// ValidatorInterface 验证器接口
type ValidatorInterface interface {
	Validate(channelID int32, rows [][]string, tableName, sheetName string, checkInfo map[int32]model.ConfigCheck) error
}

// NewDataProcessorService 创建数据处理服务
func NewDataProcessorService(enumProcessor EnumProcessorInterface, structBuilder StructBuilderInterface, validator ValidatorInterface) *DataProcessorService {
	return &DataProcessorService{
		enumProcessor: enumProcessor,
		structBuilder: structBuilder,
		validator:     validator,
	}
}

// ProcessEnumSwap 处理枚举替换
func (s *DataProcessorService) ProcessEnumSwap(data map[string]*domain.XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error {
	plog.Info("开始处理枚举替换")

	for tableName, table := range data {
		err := s.replaceEnumsInTable(tableName, table, enumSwapDict, ignoreLine)
		if err != nil {
			return fmt.Errorf("处理表 %s 的枚举替换失败: %w", tableName, err)
		}
	}

	plog.Info("枚举替换处理完成")
	return nil
}

// ProcessChannelData 处理渠道数据
func (s *DataProcessorService) ProcessChannelData(channelData map[int32]map[string]*domain.XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error {
	plog.Info("开始处理渠道数据枚举替换")

	for channelID, data := range channelData {
		plog.Infof("处理渠道 %d 的数据", channelID)
		err := s.ProcessEnumSwap(data, enumSwapDict, ignoreLine)
		if err != nil {
			return fmt.Errorf("处理渠道 %d 数据失败: %w", channelID, err)
		}
	}

	plog.Info("渠道数据处理完成")
	return nil
}

// ParseTable 解析表格数据
func (s *DataProcessorService) ParseTable(channelID int32, tableName string, info *domain.XlsxInfo, config *domain.ProcessConfig) (map[int]interface{}, error) {
	plog.Infof("开始解析表格: %s (渠道: %d)", tableName, channelID)

	totalStructs := make(map[int]interface{})

	for subTableName, rows := range info.Rows {
		plog.Infof("解析子表: %s.%s", tableName, subTableName)

		// 替换字段枚举
		if err := s.enumProcessor.SwapEnum(channelID, rows, info.Enums, nil); err != nil {
			return nil, fmt.Errorf("替换枚举失败: %w", err)
		}
		plog.Infof("枚举替换完成: %s.%s", tableName, subTableName)

		// 参数展开 (TODO: 实现参数展开逻辑)
		if err := s.expandParam(rows); err != nil {
			return nil, fmt.Errorf("参数展开失败: %w", err)
		}

		// 配置检查
		if err := s.validator.Validate(channelID, rows, tableName, subTableName, config.CheckInfo); err != nil {
			return nil, fmt.Errorf("参数检查失败: %w", err)
		}
		plog.Infof("数据验证完成: %s.%s", tableName, subTableName)

		// 创建数据结构
		isConstant := config.ConstantDict[tableName]
		isMissRepeat := config.MissRepSheet[tableName]

		data, err := s.structBuilder.CreateStruct(rows, tableName, isConstant, isMissRepeat, config.SnakeCase)
		if err != nil {
			return nil, fmt.Errorf("创建数据结构失败: %w", err)
		}
		plog.Infof("数据结构创建完成: %s.%s", tableName, subTableName)

		// 合并数据
		utils.MergeMap(totalStructs, data)
		plog.Infof("数据合并完成: %s.%s", tableName, subTableName)
	}

	plog.Infof("表格解析完成: %s", tableName)
	return totalStructs, nil
}

// ValidateData 验证数据
func (s *DataProcessorService) ValidateData(channelID int32, rows [][]string, tableName, sheetName string, checkInfo map[int32]model.ConfigCheck) error {
	return s.validator.Validate(channelID, rows, tableName, sheetName, checkInfo)
}

// replaceEnumsInTable 在表格中替换枚举
func (s *DataProcessorService) replaceEnumsInTable(tableName string, table *domain.XlsxInfo, enumSwapDict map[string]map[string]string, ignoreLine int) error {
	const SEGMENT_SPLIT = "|" // 定义分段分隔符

	for k, row := range table.Rows {
		for rowIdx := ignoreLine; rowIdx < len(row); rowIdx++ {
			rowNow := row[rowIdx]
			valueNew := table.Rows[k][rowIdx]

			for cellIndex, cellValue := range rowNow {
				// segment 替换
				if len(row) > 3 && cellIndex < len(row[3]) && row[3][cellIndex] == "segment" &&
					len(row) > 0 && cellIndex < len(row[0]) && row[0][cellIndex] != "string" {
					split := strings.Split(cellValue, SEGMENT_SPLIT)
					newValues := make([]string, 0, len(split))

					for _, v := range split {
						if findStr, exists := enumSwapDict["enum"][v]; exists {
							newValues = append(newValues, findStr)
						} else {
							newValues = append(newValues, v)
						}
					}

					valueNew[cellIndex] = strings.Join(newValues, SEGMENT_SPLIT)
					continue
				}

				// 普通枚举替换
				if findStr, exists := enumSwapDict["enum"][cellValue]; exists {
					valueNew[cellIndex] = findStr
					plog.Infof("替换系统枚举 %s: %v -> %v", tableName, cellValue, findStr)
				}
			}
		}
	}

	return nil
}

// expandParam 参数展开 (TODO: 实现具体逻辑)
func (s *DataProcessorService) expandParam(rows [][]string) error {
	// TODO: 实现参数展开逻辑
	return nil
}

// CheckIDConflicts 检查ID冲突
func (s *DataProcessorService) CheckIDConflicts(newData map[int]interface{}, existingOutput map[string]map[int]interface{}, tableName string, isConstant bool) error {
	if isConstant {
		return nil // 常量表不检查ID冲突
	}

	for _, otherTableDict := range existingOutput {
		for id := range otherTableDict {
			if newData[id] != nil && id != -1 {
				return fmt.Errorf("表 %s 存在ID冲突: id=%d", tableName, id)
			}
		}
	}

	return nil
}
