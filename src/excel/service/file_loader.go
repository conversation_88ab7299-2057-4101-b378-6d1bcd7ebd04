package service

import (
	"fmt"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/utils"
	"tcloud/wolong/utils/plog"
)

// FileLoaderService 文件加载服务实现
type FileLoaderService struct {
	xlsxReader XlsxReaderInterface
}

// XlsxReaderInterface Excel读取器接口
type XlsxReaderInterface interface {
	Read(tableName string, filePath string, sheets []string, enums []conf.EnumItem) (*domain.XlsxInfo, error)
	ReadEnumPublic(tableName string, filePath string, sheets []string, enums []conf.EnumItem) (*domain.XlsxInfo, error)
}

// NewFileLoaderService 创建文件加载服务
func NewFileLoaderService(xlsxReader XlsxReaderInterface) *FileLoaderService {
	return &FileLoaderService{
		xlsxReader: xlsxReader,
	}
}

// LoadEnumFile 加载枚举文件
func (s *FileLoaderService) LoadEnumFile(filePath string) (*domain.XlsxInfo, error) {
	plog.Infof("Loading enum file: %s", filePath)

	enumInfo, err := s.xlsxReader.ReadEnumPublic("enum", filePath, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("读取枚举表失败: %w", err)
	}

	plog.Info("Enum file loaded successfully")
	return enumInfo, nil
}

// LoadTableFiles 加载表格文件
func (s *FileLoaderService) LoadTableFiles(tables map[string]*conf.Table, templatePath string) (map[string]*domain.XlsxInfo, error) {
	plog.Infof("Loading table files from: %s", templatePath)

	result := make(map[string]*domain.XlsxInfo)

	for tblName, tbl := range tables {
		if tbl.Duplicate { // 对于同一张表的重复映射，不再重复解析
			continue
		}

		// 验证表类型
		if err := s.validateTableType(tbl.TableType); err != nil {
			return nil, fmt.Errorf("table %s: %w", tblName, err)
		}

		plog.Infof("Loading table: %s, config: %+v", tblName, *tbl)

		info, err := s.loadSingleTable(tblName, *tbl, templatePath)
		if err != nil {
			return nil, fmt.Errorf("failed to load table %s: %w", tblName, err)
		}

		result[tblName] = info
	}

	plog.Infof("Successfully loaded %d tables", len(result))
	return result, nil
}

// LoadChannelFiles 加载渠道文件
func (s *FileLoaderService) LoadChannelFiles(tables map[string]*conf.Table, channels []conf.ChannelInfo, templatePath string) (map[int32]map[string]*domain.XlsxInfo, error) {
	plog.Infof("Loading channel files for %d channels", len(channels))

	result := make(map[int32]map[string]*domain.XlsxInfo)

	for _, channel := range channels {
		channelData := make(map[string]*domain.XlsxInfo)

		for tblName, tbl := range tables {
			if tbl.Duplicate {
				continue
			}

			channelFilePath := fmt.Sprintf("%s/%s", channel.Path, tbl.Workbook)

			if !utils.IsFileExist(channelFilePath) {
				continue // 渠道文件不存在，跳过
			}

			plog.Infof("Loading channel file: %s for channel %d", channelFilePath, channel.Id)

			info, err := s.loadSingleTable(tblName, *tbl, channel.Path)
			if err != nil {
				return nil, fmt.Errorf("failed to load channel file %s for channel %d: %w", channelFilePath, channel.Id, err)
			}

			channelData[tblName] = info
		}

		if len(channelData) > 0 {
			result[channel.Id] = channelData
		}
	}

	plog.Infof("Successfully loaded channel files for %d channels", len(result))
	return result, nil
}

// LoadLocaleFile 加载多语言文件
func (s *FileLoaderService) LoadLocaleFile(filePath string) (*domain.XlsxInfo, error) {
	plog.Infof("Loading locale file: %s", filePath)

	localeInfo, err := s.xlsxReader.Read("locale", filePath, nil, nil)
	if err != nil {
		plog.Info("无多语言表，不进行多语言处理")
		return nil, nil // 多语言文件不是必需的
	}

	plog.Info("Locale file loaded successfully")
	return localeInfo, nil
}

// validateTableType 验证表类型
func (s *FileLoaderService) validateTableType(tableType string) error {
	switch tableType {
	case "", conf.TableTypeClientOnly, conf.TableTypeServerOnly, conf.TableTypeShared:
		return nil
	default:
		return fmt.Errorf("invalid table type: %s", tableType)
	}
}

// loadSingleTable 加载单个表格
func (s *FileLoaderService) loadSingleTable(tableName string, table conf.Table, basePath string) (*domain.XlsxInfo, error) {
	filePath := fmt.Sprintf("%s/%s", basePath, table.Workbook)

	info, err := s.xlsxReader.Read(tableName, filePath, table.Sheet, table.Enums)
	if err != nil {
		return nil, fmt.Errorf("读取%s.%s失败: %w", table.Workbook, table.Sheet, err)
	}

	return info, nil
}
