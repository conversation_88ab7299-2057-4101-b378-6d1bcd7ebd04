package service

import (
	"fmt"
	"os"
	"tcloud/wolong/cmn"
	"tcloud/wolong/conf"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/utils/plog"
)

// CodeGeneratorService 代码生成服务实现
type CodeGeneratorService struct{}

// NewCodeGeneratorService 创建代码生成服务
func NewCodeGeneratorService() *CodeGeneratorService {
	return &CodeGeneratorService{}
}

// GenerateGolangStub 生成Golang桩代码
func (s *CodeGeneratorService) GenerateGolangStub(data map[string]map[int]interface{}, config *domain.CodeGenConfig) error {
	plog.Info("开始生成Golang桩代码")

	// 创建Go代码目录
	goDir := fmt.Sprintf("%s/%s/", config.StubDir, cmn.FileGoPackage)
	if err := os.MkdirAll(goDir, 0777); err != nil {
		return fmt.Errorf("创建Go代码目录失败: %w", err)
	}

	loadFuncNames := make([]string, 0, 16)

	for fileName, tableData := range data {
		if !conf.OutputGo(fileName) {
			continue
		}

		// 添加调试日志
		plog.Infof("表 %s 的 table_type: %s", fileName, config.TableType[fileName])

		// 修改过滤逻辑：client_only 表不生成 golang 桩代码，shared 和 server_only 表生成
		if config.TableType[fileName] == conf.TableTypeClientOnly {
			plog.Infof("跳过 client_only 表: %s", fileName)
			continue
		}

		// 生成代码
		if err := s.generateGolangTableCode(fileName, tableData, goDir, config); err != nil {
			return fmt.Errorf("生成表 %s 的Golang代码失败: %w", fileName, err)
		}

		loadFuncNames = append(loadFuncNames, fileName)
	}

	plog.Infof("Golang桩代码生成完成，保存在: %s", goDir)
	return nil
}

// GenerateCsharpStub 生成C#桩代码
func (s *CodeGeneratorService) GenerateCsharpStub(data map[string]map[int]interface{}, config *domain.CodeGenConfig) error {
	plog.Info("开始生成C#桩代码")

	// 创建C#代码目录
	csDir := fmt.Sprintf("%s/cs/", config.StubDir)
	if err := os.MkdirAll(csDir, 0777); err != nil {
		return fmt.Errorf("创建C#代码目录失败: %w", err)
	}

	for fileName, tableData := range data {
		if !conf.OutputCs(fileName) {
			continue
		}

		// 修改过滤逻辑：client_only 和 shared 表生成 C# 桩代码
		tableType := config.TableType[fileName]
		if tableType != conf.TableTypeClientOnly && tableType != conf.TableTypeShared && tableType != "" {
			continue
		}

		// 生成代码
		if err := s.generateCsharpTableCode(fileName, tableData, csDir, config); err != nil {
			return fmt.Errorf("生成表 %s 的C#代码失败: %w", fileName, err)
		}
	}

	plog.Infof("C#桩代码生成完成，保存在: %s", csDir)
	return nil
}

// generateGolangTableCode 生成单个表的Golang代码
func (s *CodeGeneratorService) generateGolangTableCode(fileName string, tableData map[int]interface{}, outputDir string, config *domain.CodeGenConfig) error {
	plog.Infof("生成Golang代码: %s", fileName)

	// 获取第一个数据项用于生成结构
	var sampleData interface{}
	for _, v := range tableData {
		sampleData = v
		break
	}

	if sampleData == nil {
		plog.Infof("表 %s 没有数据，跳过代码生成", fileName)
		return nil
	}

	// TODO: 这里需要实现实际的Golang代码生成逻辑
	// 为了避免循环导入，我们暂时返回一个简单的实现
	isConstTable := config.ConstantDict[fileName]

	if isConstTable {
		plog.Infof("生成常量文件: %s", fileName)
	}

	// 简单的代码生成逻辑
	plog.Infof("生成Golang代码: %s (数据类型: %T)", fileName, sampleData)

	plog.Infof("Golang代码生成完成: %s", fileName)
	return nil
}

// generateCsharpTableCode 生成单个表的C#代码
func (s *CodeGeneratorService) generateCsharpTableCode(fileName string, tableData map[int]interface{}, outputDir string, config *domain.CodeGenConfig) error {
	plog.Infof("生成C#代码: %s", fileName)

	// 获取第一个数据项用于生成结构
	var sampleData interface{}
	for _, v := range tableData {
		sampleData = v
		break
	}

	if sampleData == nil {
		plog.Infof("表 %s 没有数据，跳过代码生成", fileName)
		return nil
	}

	// TODO: 这里需要实现实际的C#代码生成逻辑
	// 为了避免循环导入，我们暂时返回一个简单的实现
	csFileName := cmn.CamelName(fileName)

	// 简单的代码生成逻辑
	plog.Infof("生成C#代码: %s (数据类型: %T)", csFileName, sampleData)

	plog.Infof("C#代码生成完成: %s", fileName)
	return nil
}
