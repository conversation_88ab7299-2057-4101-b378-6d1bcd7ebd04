package service

import (
	"tcloud/wolong/deploy"
	"tcloud/wolong/model"
	"tcloud/wolong/utils/plog"
)

// ConfigCheckerService 配置检查服务实现
type ConfigCheckerService struct{}

// NewConfigCheckerService 创建配置检查服务
func NewConfigCheckerService() *ConfigCheckerService {
	return &ConfigCheckerService{}
}

// BuildConfigCheck 构建配置检查信息
func (s *ConfigCheckerService) BuildConfigCheck() map[int32]model.ConfigCheck {
	plog.Info("开始构建配置检查信息")

	checkInfo := deploy.GetConfigCheck()

	plog.Infof("配置检查信息构建完成，渠道数量: %d", len(checkInfo))
	return checkInfo
}
