package factory

import (
	"tcloud/wolong/excel/adapter"
	"tcloud/wolong/excel/domain"
	"tcloud/wolong/excel/service"
)

// ParserFactory 解析器工厂
type ParserFactory struct{}

// NewParserFactory 创建解析器工厂
func NewParserFactory() *ParserFactory {
	return &ParserFactory{}
}

// CreateParserService 创建解析器服务
func (f *ParserFactory) CreateParserService(config *service.ParserConfig) *service.ParserService {
	// 创建基础服务
	fileLoader := f.createFileLoader()
	dataProcessor := f.createDataProcessor()
	outputManager := f.createOutputManager()
	codeGenerator := f.createCodeGenerator()
	enumProcessor := f.createEnumProcessor()
	configChecker := f.createConfigChecker()

	// 创建解析器服务
	return service.NewParserService(
		fileLoader,
		dataProcessor,
		outputManager,
		codeGenerator,
		enumProcessor,
		configChecker,
		config,
	)
}

// createFileLoader 创建文件加载器
func (f *ParserFactory) createFileLoader() domain.FileLoader {
	xlsxReader := adapter.NewXlsxReaderAdapter(false, false)
	return service.NewFileLoaderService(xlsxReader)
}

// createDataProcessor 创建数据处理器
func (f *ParserFactory) createDataProcessor() domain.DataProcessor {
	enumProcessor := f.createEnumProcessorService()
	structBuilder := adapter.NewStructBuilderAdapter()

	// 创建一个简单的解析器接口实现用于验证器
	parserData := &SimpleParserData{
		channelXlsx: make(map[int32]map[string]*domain.XlsxInfo),
		xlsx:        make(map[string]*domain.XlsxInfo),
	}
	validator := adapter.NewValidatorAdapter(parserData)

	return service.NewDataProcessorService(enumProcessor, structBuilder, validator)
}

// createOutputManager 创建输出管理器
func (f *ParserFactory) createOutputManager() domain.OutputManager {
	return service.NewOutputManagerService()
}

// createCodeGenerator 创建代码生成器
func (f *ParserFactory) createCodeGenerator() domain.CodeGenerator {
	return service.NewCodeGeneratorService()
}

// createEnumProcessor 创建枚举处理器
func (f *ParserFactory) createEnumProcessor() domain.EnumProcessor {
	return service.NewEnumProcessorService()
}

// createEnumProcessorService 创建枚举处理器服务（用于数据处理器）
func (f *ParserFactory) createEnumProcessorService() service.EnumProcessorInterface {
	return service.NewEnumProcessorService()
}

// createConfigChecker 创建配置检查器
func (f *ParserFactory) createConfigChecker() domain.ConfigChecker {
	return service.NewConfigCheckerService()
}

// SimpleParserData 简单的解析器数据实现
type SimpleParserData struct {
	channelXlsx map[int32]map[string]*domain.XlsxInfo
	xlsx        map[string]*domain.XlsxInfo
}

// GetChannelXlsx 获取渠道Excel数据
func (s *SimpleParserData) GetChannelXlsx() map[int32]map[string]*domain.XlsxInfo {
	return s.channelXlsx
}

// GetXlsx 获取Excel数据
func (s *SimpleParserData) GetXlsx() map[string]*domain.XlsxInfo {
	return s.xlsx
}

// SetChannelXlsx 设置渠道Excel数据
func (s *SimpleParserData) SetChannelXlsx(data map[int32]map[string]*domain.XlsxInfo) {
	s.channelXlsx = data
}

// SetXlsx 设置Excel数据
func (s *SimpleParserData) SetXlsx(data map[string]*domain.XlsxInfo) {
	s.xlsx = data
}
